{% extends "base.html" %}

{% block title %}Submissions{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header with animated gradient -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl overflow-hidden mb-8 shadow-lg transform transition-all duration-300 hover:shadow-xl">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8">
            <div class="text-center md:text-left">
                <h1 class="text-3xl font-bold text-white tracking-tight">
                    {% if session.user_id and user and user.role == 'admin' %}
                        All Submissions
                    {% else %}
                        Your Submissions
                    {% endif %}
                </h1>
                <p class="mt-2 text-indigo-100 max-w-2xl">
                    {% if session.user_id and user and user.role == 'admin' %}
                        View and monitor all student submissions across the platform
                    {% else %}
                        Track your progress and review your answers
                    {% endif %}
                </p>
            </div>
            </div>
        </div>
    </div>

    <!-- Submissions List -->
    <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
        <div class="p-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                <!-- Left Side: Title -->
                <div class="flex items-center">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                        <i class="fas fa-history"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">
                            {% if session.user_id and user and user.role == 'admin' %}
                                All Student Submissions
                            {% else %}
                                Submission History
                            {% endif %}
                        </h2>
                        <p class="text-sm text-gray-500">
                            {% if session.user_id and user and user.role == 'admin' %}
                                Showing latest submissions from all users (sorted by time)
                            {% else %}
                                Showing latest submissions first
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <!-- Right Side: Filters -->
                <form method="GET" action="{{ url_for('submissions') }}">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                        <!-- Username Search (Admin Only) -->
                        {% if session.user_id and user and user.role == 'admin' %}
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search h-4 w-4 text-gray-400"></i>
                            </div>
                            <input type="text"
                                   name="username"
                                   id="username"
                                   value="{{ selected_username or '' }}"
                                   placeholder="Search by username..."
                                   class="pl-10 pr-10 block w-full sm:w-48 rounded-md border-gray-300 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6 transition duration-150 ease-in-out">
                            {% if selected_username %}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="clearUsernameSearch()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                    <i class="fas fa-times h-4 w-4"></i>
                                </button>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Filter Dropdowns -->
                        <div class="flex items-center space-x-2">
                            <select name="subject_id" id="subject_id" onchange="this.form.submit()" class="block w-full sm:w-auto rounded-md border-gray-300 py-1.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6 transition duration-150 ease-in-out">
                                <option value="">Filter by Subject</option>
                                {% for subject in subjects_for_filter %}
                                    <option value="{{ subject.id }}" {% if subject.id == selected_subject_id %}selected{% endif %}>{{ subject.name }}</option>
                                {% endfor %}
                            </select>

                            <select name="topic_id" id="topic_id" onchange="this.form.submit()" class="block w-full sm:w-auto rounded-md border-gray-300 py-1.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6 transition duration-150 ease-in-out">
                                <option value="">Filter by Topic</option>
                                {% for topic in topics_for_filter %}
                                    <option value="{{ topic.id }}" {% if topic.id == selected_topic_id %}selected{% endif %}>{{ topic.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Search Button (Admin Only) -->
                        {% if session.user_id and user and user.role == 'admin' %}
                        <button type="submit" class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 transition-colors duration-200">
                            <i class="fas fa-search mr-1.5"></i>
                            Search
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>

            {% if submissions %}
                <div class="grid gap-4">
                    {% for submission in submissions %}
                    <div class="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md hover:bg-white transform hover:-translate-y-1 border border-gray-100">
                        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <!-- User and Time -->
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-medium mr-3">
                                    {{ submission.user.username[:1].upper() }}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">{{ submission.user.username }}</div>
                                    <div class="text-xs text-gray-500">
                                        <i class="fas fa-clock mr-1"></i>
                                        {{ submission.timestamp.strftime('%b %d, %Y at %H:%M') }}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Question -->
                            <div class="flex-grow">
                                <a href="{{ url_for('load_question', question_id=submission.question_id) }}" 
                                   class="text-gray-900 hover:text-indigo-600 transition-colors duration-200 font-medium">
                                    {{ submission.question.title }}
                                </a>
                                <div class="text-xs text-gray-500 mt-1">
                                    <span class="inline-flex items-center">
                                        <i class="fas fa-tag mr-1"></i>
                                        {{ submission.part.description }}
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Score -->
                            <div class="flex items-center space-x-4">
                                <div class="flex flex-col items-center">
                                    <div class="relative w-12 h-12">
                                        <svg class="w-full h-full" viewBox="0 0 36 36">
                                            <path class="stroke-current text-gray-200" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <path class="stroke-current {% if submission.score == submission.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) or 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <text x="18" y="20.5" class="fill-current {% if submission.score == submission.part.score %}text-green-700{% elif submission.score > 0 %}text-amber-700{% else %}text-red-700{% endif %} font-bold text-xs" text-anchor="middle">{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) | int or 0 }}%</text>
                                        </svg>
                                    </div>
                                    <div class="text-xs font-medium {% if submission.score == submission.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %} mt-1">
                                        {{ submission.score }}/{{ submission.part.score }}
                                    </div>
                                </div>
                                
                                <a href="{{ url_for('submission_details', submission_id=submission.id) }}" 
                                   class="inline-flex items-center justify-center rounded-md bg-indigo-50 px-3.5 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                                    <span>View Details</span>
                                    <i class="fas fa-chevron-right ml-1.5 text-xs opacity-70 group-hover:translate-x-0.5 transition-transform duration-200"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if pagination and pagination.pages > 1 %}
                <div class="mt-8 flex items-center justify-between">
                    <!-- Mobile pagination -->
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if pagination.has_prev %}
                            <a href="{{ url_for('submissions', page=pagination.prev_num, subject_id=selected_subject_id, topic_id=selected_topic_id, username=selected_username) }}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                Previous
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                                Previous
                            </span>
                        {% endif %}

                        {% if pagination.has_next %}
                            <a href="{{ url_for('submissions', page=pagination.next_num, subject_id=selected_subject_id, topic_id=selected_topic_id, username=selected_username) }}"
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                Next
                            </a>
                        {% else %}
                            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                                Next
                            </span>
                        {% endif %}
                    </div>

                    <!-- Desktop pagination -->
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ ((pagination.page - 1) * pagination.per_page) + 1 }}</span>
                                to <span class="font-medium">{{ ((pagination.page - 1) * pagination.per_page) + submissions|length }}</span>
                                of <span class="font-medium">{{ pagination.total }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <!-- Previous button -->
                                {% if pagination.has_prev %}
                                    <a href="{{ url_for('submissions', page=pagination.prev_num, subject_id=selected_subject_id, topic_id=selected_topic_id, username=selected_username) }}"
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                {% else %}
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </span>
                                {% endif %}

                                <!-- Page numbers -->
                                {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                    {% if page_num %}
                                        {% if page_num != pagination.page %}
                                            <a href="{{ url_for('submissions', page=page_num, subject_id=selected_subject_id, topic_id=selected_topic_id, username=selected_username) }}"
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                                {{ page_num }}
                                            </a>
                                        {% else %}
                                            <span aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                                {{ page_num }}
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                    {% endif %}
                                {% endfor %}

                                <!-- Next button -->
                                {% if pagination.has_next %}
                                    <a href="{{ url_for('submissions', page=pagination.next_num, subject_id=selected_subject_id, topic_id=selected_topic_id, username=selected_username) }}"
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                {% else %}
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </span>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <div class="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-lg">
                    <div class="w-24 h-24 rounded-full bg-indigo-50 flex items-center justify-center mb-4">
                        <i class="fas fa-inbox text-indigo-300 text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-1">No submissions found</h3>
                    <p class="text-gray-500 text-center max-w-md">
                        {% if session.user_id and user and user.role == 'admin' %}
                            No student submissions found. Students haven't submitted any answers yet.
                        {% elif user %}
                            {{ user.username }} hasn't submitted any answers yet.
                        {% else %}
                            There are no submissions to display. Try solving some questions first!
                        {% endif %}
                    </p>
                    <a href="{{ url_for('vault') }}" 
                       class="mt-6 inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 transition-colors duration-200">
                        <i class="fas fa-book-open mr-2"></i>
                        Go to Question Vault
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }
    
    /* Subtle animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Circular progress animation */
    @keyframes progress {
        0% {
            stroke-dasharray: 0 100;
        }
    }
    
    .stroke-current {
        animation: progress 1s ease-out forwards;
    }
</style>

<script>
    // Clear username search function
    function clearUsernameSearch() {
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.value = '';
            usernameInput.form.submit();
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle Enter key in username search
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    this.form.submit();
                }
            });
        }


        // Add staggered animation to submission cards
        const cards = document.querySelectorAll('.bg-gray-50');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(10px)';
            card.style.transition = 'all 0.3s ease-out';
            card.style.transitionDelay = `${index * 0.05}s`;
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }); 
    });
</script>
{% endblock %}
